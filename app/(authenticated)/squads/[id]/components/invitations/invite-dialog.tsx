"use client"

import { useState, use<PERSON>em<PERSON>, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Mail, Link2, <PERSON><PERSON>, Check, Users, Clock, Send } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { UserEmailAutocomplete } from "@/components/ui/user-email-autocomplete"
import {
  useGenerateInvitationLink,
  useInvitationLink,
  useSendInvitationEmails,
  useRealtimeSquadInvitationSends,
} from "@/lib/domains/invitation/invitation.hooks"
import {
  useUserSquads,
  useIsSquadLeader,
  useSquadMembersData,
} from "@/lib/domains/squad/squad.hooks"
import { generateInvitationLink } from "@/lib/email-service"
import { UserService } from "@/lib/domains/user/user.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"

interface InviteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  squadId: string
  squadName: string
  onInviteSent?: () => void
  onRedirectToInvitations?: () => void
}

export function InviteDialog({
  open,
  onOpenChange,
  squadId,
  squadName,
  onInviteSent,
  onRedirectToInvitations,
}: InviteDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("link")
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])
  const [copiedLink, setCopiedLink] = useState(false)
  const [emailSuggestions, setEmailSuggestions] = useState<string[]>([])

  // Get current user
  const currentUser = useUser()

  // Check if current user is squad leader
  const { isLeader } = useIsSquadLeader(squadId)

  // Hooks for invitation link management
  const { invitationLink, loading: linkLoading, refetch } = useInvitationLink(squadId)
  const { generate: generateLink, generating } = useGenerateInvitationLink(() => {
    // Refetch invitation link when a new one is created
    refetch()
  })
  const { sendEmails, sending } = useSendInvitationEmails()

  // Get invitation sends for this squad (real-time) - only for squad leaders
  const { invitationSends } = useRealtimeSquadInvitationSends(isLeader ? squadId : "")

  // Get user squads to extract email suggestions
  const { squads } = useUserSquads(false)

  // Get current squad members to exclude them from suggestions
  const { members: currentSquadMembers } = useSquadMembersData(squadId, false)

  // Generate email suggestions from squad members across all user's squads
  useEffect(() => {
    const fetchEmailSuggestions = async () => {
      if (!squads.length || !currentUser?.email) return

      const allEmails = new Set<string>()
      const excludedEmails = new Set<string>()

      try {
        // First, collect emails to exclude:
        // 1. Current user's email
        excludedEmails.add(currentUser.email.toLowerCase())

        // 2. Current squad members' emails
        if (currentSquadMembers?.length) {
          for (const member of currentSquadMembers) {
            try {
              const user = await UserService.getUser(member.userId)
              if (user?.email) {
                excludedEmails.add(user.email.toLowerCase())
              }
            } catch (error) {
              console.warn(
                `Failed to get user details for current squad member ${member.userId}:`,
                error
              )
            }
          }
        }

        // 3. Pending invitation sends only (not rejected or accepted)
        if (invitationSends?.length) {
          invitationSends.forEach((send) => {
            // Only exclude emails with pending invitations (status = "sent")
            // Allow re-inviting rejected users or users who accepted but aren't in squad
            if (send.email && send.status === "sent") {
              excludedEmails.add(send.email.toLowerCase())
            }
          })
        }

        // Now collect emails from all other squads
        for (const squad of squads) {
          // Skip the current squad since we don't want to suggest its members
          if (squad.id === squadId) continue

          // Get squad members
          const members = await import("@/lib/domains/squad/squad.service").then(
            ({ SquadService }) => SquadService.getSquadMembers(squad.id)
          )

          // For each member, get their user details to extract email
          for (const member of members) {
            try {
              const user = await UserService.getUser(member.userId)
              if (user?.email && !excludedEmails.has(user.email.toLowerCase())) {
                allEmails.add(user.email)
              }
            } catch (error) {
              console.warn(`Failed to get user details for ${member.userId}:`, error)
            }
          }
        }

        setEmailSuggestions(Array.from(allEmails))
      } catch (error) {
        console.error("Error fetching email suggestions:", error)
      }
    }

    fetchEmailSuggestions()
  }, [squads, currentUser?.email, currentUser?.uid, squadId, currentSquadMembers, invitationSends])

  // Generate or get invitation link
  const handleGenerateLink = async () => {
    if (invitationLink) return invitationLink

    const newLink = await generateLink(squadId, squadName)
    if (newLink) {
      toast({
        title: "Invitation link generated",
        description: "Your shareable invitation link is ready!",
      })
    }
    return newLink
  }

  // Copy invitation link to clipboard
  const handleCopyLink = async () => {
    const link = invitationLink || (await handleGenerateLink())
    if (!link) return

    const fullLink = generateInvitationLink(link.id)

    try {
      await navigator.clipboard.writeText(fullLink)
      setCopiedLink(true)
      toast({
        title: "Link copied",
        description: "Invitation link copied to clipboard",
      })

      setTimeout(() => setCopiedLink(false), 2000)
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Please copy the link manually",
        variant: "destructive",
      })
    }
  }

  // Send email invitations
  const handleSendEmails = async () => {
    if (selectedEmails.length === 0) return

    if (!invitationLink) {
      toast({
        title: "Error",
        description: "Please generate a shareable link first",
        variant: "destructive",
      })
      return
    }

    const result = await sendEmails(squadId, invitationLink.id, selectedEmails)

    if (result.success) {
      const successCount = result.results?.filter((r) => r.success).length || 0
      const failCount = (result.results?.length || 0) - successCount

      toast({
        title: "Invitations sent",
        description: `${successCount} invitation${successCount !== 1 ? "s" : ""} sent successfully${failCount > 0 ? `, ${failCount} failed` : ""}`,
      })

      setSelectedEmails([])
      if (onInviteSent) onInviteSent()

      // Redirect to invitations tab after sending
      onOpenChange(false)
      if (onRedirectToInvitations) {
        setTimeout(() => onRedirectToInvitations(), 100)
      }
    } else {
      toast({
        title: "Failed to send invitations",
        description: result.error || "Please try again",
        variant: "destructive",
      })
    }
  }

  // Format expiration date
  const formatExpirationDate = (expiresAt: any) => {
    if (!expiresAt) return ""
    const date = expiresAt.toDate ? expiresAt.toDate() : new Date(expiresAt)
    return date.toLocaleDateString()
  }

  // Get recent invitation sends for display
  const recentSends = useMemo(() => {
    if (!invitationLink || !invitationSends) return []

    return invitationSends
      .filter((send) => send.invitationId === invitationLink.id)
      .sort((a, b) => {
        const aDate = a.sentAt?.toDate ? a.sentAt.toDate() : new Date()
        const bDate = b.sentAt?.toDate ? b.sentAt.toDate() : new Date()
        return bDate.getTime() - aDate.getTime()
      })
      .slice(0, 5) // Show last 5 sends
  }, [invitationLink, invitationSends])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-lg sm:text-xl truncate">Invite to {squadName}</DialogTitle>
          <DialogDescription className="text-sm leading-relaxed">
            Share an invitation link or send individual email invitations to grow your squad.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="link" className="flex items-center gap-1 sm:gap-2 text-sm">
              <Link2 className="h-4 w-4 flex-shrink-0" />
              <span className="hidden sm:inline">Shareable Link</span>
              <span className="sm:hidden">Link</span>
            </TabsTrigger>
            <TabsTrigger
              value="email"
              className="flex items-center gap-1 sm:gap-2 text-sm"
              disabled={!invitationLink}
            >
              <Mail className="h-4 w-4 flex-shrink-0" />
              <span className="hidden sm:inline">Email Invitations</span>
              <span className="sm:hidden">Email</span>
              {!invitationLink && (
                <Badge variant="secondary" className="text-xs ml-1 hidden sm:inline-flex">
                  Link Required
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="link" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                <Clock className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                <span className="truncate">
                  {invitationLink
                    ? `Expires on ${formatExpirationDate(invitationLink.expiresAt)}`
                    : "Links expire after 10 days"}
                </span>
              </div>

              {invitationLink ? (
                <div className="space-y-3">
                  <div className="flex flex-col sm:flex-row gap-2 p-3 bg-muted rounded-md">
                    <Input
                      value={generateInvitationLink(invitationLink.id)}
                      readOnly
                      className="flex-1 bg-background text-sm"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyLink}
                      disabled={linkLoading}
                      className="w-full sm:w-auto flex items-center gap-2"
                    >
                      {copiedLink ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      <span className="sm:hidden">{copiedLink ? "Copied!" : "Copy Link"}</span>
                    </Button>
                  </div>

                  <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                    <Users className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span>Anyone with this link can join your squad</span>
                  </div>
                </div>
              ) : (
                <div className="text-center py-6">
                  <Button
                    onClick={handleGenerateLink}
                    disabled={generating || linkLoading}
                    className="flex items-center gap-2 w-full sm:w-auto"
                  >
                    <Link2 className="h-4 w-4 flex-shrink-0" />
                    <span className="hidden sm:inline">
                      {generating ? "Generating..." : "Generate Invitation Link"}
                    </span>
                    <span className="sm:hidden">
                      {generating ? "Generating..." : "Generate Link"}
                    </span>
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="email" className="space-y-4">
            {!invitationLink ? (
              <div className="text-center py-6 sm:py-8 space-y-4 px-4">
                <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                  <Link2 className="h-6 w-6 sm:h-8 sm:w-8 text-muted-foreground" />
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-sm sm:text-base">
                    Generate a shareable link first
                  </h3>
                  <p className="text-xs sm:text-sm text-muted-foreground max-w-sm mx-auto leading-relaxed">
                    You need to create a shareable invitation link before you can send email
                    invitations. The link will be included in the invitation emails.
                  </p>
                </div>
                <Button
                  onClick={() => setActiveTab("link")}
                  variant="outline"
                  className="flex items-center gap-2 w-full sm:w-auto"
                  size="sm"
                >
                  <Link2 className="h-4 w-4 flex-shrink-0" />
                  <span className="hidden sm:inline">Go to Shareable Link</span>
                  <span className="sm:hidden">Go to Link Tab</span>
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="emails">Email Addresses</Label>
                  <UserEmailAutocomplete
                    value={selectedEmails}
                    onChange={setSelectedEmails}
                    suggestions={emailSuggestions}
                    placeholder="Enter email addresses..."
                    maxEmails={10}
                    className="mt-2"
                  />
                </div>

                {recentSends.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-xs sm:text-sm font-medium">Recent Invitations</Label>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {recentSends.map((send) => (
                        <div
                          key={send.id}
                          className="flex items-center justify-between p-2 bg-muted rounded-sm text-xs sm:text-sm gap-2"
                        >
                          <span className="flex items-center gap-2 min-w-0 flex-1">
                            <Mail className="h-3 w-3 flex-shrink-0" />
                            <span className="truncate">{send.email}</span>
                          </span>
                          <Badge variant="secondary" className="text-xs flex-shrink-0">
                            {send.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 sm:gap-0 sm:items-center sm:justify-between">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto"
          >
            Close
          </Button>

          {activeTab === "email" && invitationLink && (
            <Button
              onClick={handleSendEmails}
              disabled={sending || selectedEmails.length === 0}
              className="flex items-center gap-2 w-full sm:w-auto"
            >
              <Send className="h-4 w-4 flex-shrink-0" />
              <span className="hidden sm:inline">
                {sending
                  ? "Sending..."
                  : `Send ${selectedEmails.length} Invitation${selectedEmails.length !== 1 ? "s" : ""}`}
              </span>
              <span className="sm:hidden">
                {sending
                  ? "Sending..."
                  : selectedEmails.length > 0
                    ? `Send ${selectedEmails.length}`
                    : "Send"}
              </span>
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
